import { getSnapshot, Instance, SnapshotIn, SnapshotOut, types } from "mobx-state-tree";
import { ZPolygon, ZRect } from "z_polygon";

import { StorePolyline } from "../store/StorePolyline";
import { EntityBase } from "./EntityBase";
import { EntityType } from "./EntityType";
import { TRoomShape } from "../service/room/TRoomShape";
import { WPolygon } from "./WPolygon";
import { Vector3 } from "three";
import { IStorePoint3, StorePoint3 } from "../store/StorePoint3";


/**
* @description 房间实体
* <AUTHOR>
* @date 2025-06-17
* @lastEditTime 2025-06-17 11:05:25
* @lastEditors xuld
*/
export const EntityRoom = types
    .compose(EntityBase, types.model({
        // 是否可见，默认为 true 可见
        name: types.string,
        visible: types.optional(types.boolean, true),
        height: types.optional(types.number, 2800),
        profile: StorePolyline,
    }))
    .named(EntityType.room).props({
        type: EntityType.room
    })
    .actions(self => ({
        // 设置是否可见
        setVisible(visible: boolean) {
            self.visible = visible;
        },
        setHeight(height: number) {
            self.height = height;
        },
        updateByPoint(points: Vector3[]) {
            let vertices: IStorePoint3[] = [];
            points.forEach(point => {
                vertices.push(StorePoint3.create({ x: point.x, y: point.y, z: point.z }));
            });
            self.profile.setVertices(vertices);
        }
    }))
    .views(self => ({
        get dto(): IEntityRoomSnapshotOut {
            let obj = getSnapshot(self);
            return {
                ...obj,
            }
        },
        get roomPoly(): ZPolygon {
            return self.profile.polygon;
        },
        get rect(): ZRect {
            // 默认矩形是最大包围盒矩形
            let rect = ZRect.fromBox3(this.roomPoly.computeBBox());
            if (rect.w < rect.h) {
                rect.swapWidthAndHeight();
            }
            return rect;
        },
        get mainRect(): ZRect {
            let mRect = TRoomShape.computeMaxRectBySplitShape(this.roomPoly);
            let polys = WPolygon.splitPolyIntoRects(this.roomPoly, "SplitRoom");
            for (let poly of polys) {
                if (poly.edges.length != 4) {
                    continue;
                }
                let rect = ZRect.computeMainRect(poly);
                if (rect.min_hh > mRect.min_hh) {
                    mRect = rect;
                }
            }
            return mRect;
        },

        get area(): number {
            let area = 0;
            let polys = WPolygon.splitPolyIntoRects(this.roomPoly, "SplitRoom");
            for (let poly of polys) {
                let rect = ZRect.computeMainRect(poly);
                let t_area = (rect.w / 1000 * rect.h / 1000);
                area += t_area;
            }
            return area;
        }
    }));


export interface IEntityRoom extends Instance<typeof EntityRoom> { }
export interface IEntityRoomSnapshotIn extends SnapshotIn<typeof EntityRoom> { }
export interface IEntityRoomSnapshotOut extends SnapshotOut<typeof EntityRoom> { }