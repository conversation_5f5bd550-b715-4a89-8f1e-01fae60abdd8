import { Instance, SnapshotIn, SnapshotOut, types } from "mobx-state-tree";

import { StoreType } from "../entity/EntityType";


/**
* @description 套系数据
"kgId": "337013",
"ruleId": 1036027,
"ruleName": "温柔治愈-奶油风",
"seriesStyle": "现代",
"seriesKgId": "337013",
"status": 2,
"thumbnail": "https://3vj-pano.3vjia.com//vr/rule/1171dea863d3490ebe23547a3a0f65f5.png",
* <AUTHOR>
* @date 2025-06-17
* @lastEditTime 2025-06-17 11:05:25
* @lastEditors xuld
*/
export const StoreSeries = types
    .model(StoreType.series, {
        kgId: types.string,
        ruleId: types.number,
        ruleName: types.string,
        seriesStyle: types.string,
        seriesKgId: types.string,
        status: types.number,
        thumbnail: types.string,
    });

export interface IStoreSeries extends Instance<typeof StoreSeries> { }
export interface IStoreSeriesSnapshotIn extends SnapshotIn<typeof StoreSeries> { }
export interface IStoreSeriesSnapshotOut extends SnapshotOut<typeof StoreSeries> { }