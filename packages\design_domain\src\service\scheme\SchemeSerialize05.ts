import { Vector3 } from "three";
import { generateUUID } from "three/src/math/MathUtils";

import { I_Door, I_SwjBaseGroup, I_SwjFurnitureData, I_SwjFurnitureGroup, I_Swj<PERSON>ineEdge, I_SwjRoom, I_SwjWall, I_SwjXmlScheme, I_Window } from "@layoutai/basic_data";

import { SchemeConst } from "../../api/SchemeConst";
import { EntityFurniture, IEntityFurniture } from "../../entity/EntityFurniture";
import { EntityFurnitureGroup } from "../../entity/EntityFurnitureGroup";
import { EntityRoom } from "../../entity/EntityRoom";
import { EntityType } from "../../entity/EntityType";
import { EntityWall } from "../../entity/EntityWall";
import { EntityWindow } from "../../entity/EntityWindow";
import { IStorePoint3, StorePoint3 } from "../../store/StorePoint3";
import { StorePolyline } from "../../store/StorePolyline";
import { ServiceManager } from "../ServiceManager";
import { ISchemeSerialize } from "./ISchemeSerialize";
import { EntityDoor } from "../../entity/EntityDoor";
import { StoreSeries } from "../../store/StoreSeries";

// 方案中套系数据格式
interface ISeries {
    kgId: string;
    ruleId: number;
    ruleName: string;
    seriesStyle: string;
    seriesKgId: string;
    status: number;
    thumbnail: string;
}

/**
* @description 方案序列化 V0.5
* <AUTHOR>
* @date 2025-06-26
* @lastEditTime 2025-06-26 17:35:15
* @lastEditors xuld
*/
export class SchemeSerialize05 implements ISchemeSerialize {

    private _uid2uuidMap: Map<string, string> = new Map();

    /**
     * @description 反序列化
     * @param schemeContentJson 方案内容
     */
    public async deserialize(schemeContentJson: I_SwjXmlScheme): Promise<void> {
        this._uid2uuidMap.clear();
        console.log("schemeContentJson", schemeContentJson);
        this.deserializeSchemeBaseInfo(schemeContentJson);
        await this.deserializeWallList(schemeContentJson.wall_list || []);
        await this.deserializeRoomList(schemeContentJson.room_list || []);
        await this.deserializeFurnitureList(schemeContentJson.furniture_list || []);
        await this.deserializeFurnitureGroupList(schemeContentJson.base_group_list || []);
        (schemeContentJson.room_list ?? []).forEach(async room => {
            await this.deserializeWindowList(room.window_list || []);
            await this.deserializeDoorList(room.door_list || []);
        });
        this.deserializeSeriesList((schemeContentJson as any).room2Series || []);
    }

    private deserializeSchemeBaseInfo(schemeContentJson: I_SwjXmlScheme): void {
        let scheme = ServiceManager.instance.storeService.getScheme();

        scheme.setId(schemeContentJson.scheme_id || "");
        scheme.setName(schemeContentJson.name || "");
        scheme.setVersion(schemeContentJson.LayoutAI_Version || SchemeConst.version_0_5);
        scheme.setLayoutId(schemeContentJson.layout_scheme_id || "");
        scheme.setWireFrameImageJsonUrl(schemeContentJson.wireFrameImageJsonUrl || "");
        scheme.setHxId(schemeContentJson.hxId || "");
    }

    private async deserializeWallList(wallList: I_SwjWall[]): Promise<void> {
        for (let i = 0; i < wallList.length; i++) {
            let wall = wallList[i];
            let uuid = generateUUID();
            if (wall.uid) {
                this._uid2uuidMap.set(wall.uid.toString(), uuid);
            }
            let entityWall = EntityWall.create({
                uuid: uuid,
                type: EntityType.wall,
                startX: wall.start_x || 0,
                startY: wall.start_y || 0,
                endX: wall.end_x || 0,
                endY: wall.end_y || 0,
                thickness: wall.thickness || 0,
                height: wall.height || 0,
            });
            ServiceManager.instance.storeService.addEntity(entityWall);
        }
    }

    private async deserializeRoomList(RoomList: I_SwjRoom[]): Promise<void> {
        for (let i = 0; i < RoomList.length; i++) {
            let room = RoomList[i];
            let boundary: I_SwjLineEdge[] = room.boundary || [];
            let vertices: IStorePoint3[] = [];
            for (let line of boundary) {
                vertices.push(StorePoint3.create({
                    x: line.start.x,
                    y: line.start.y,
                    z: line.start.z
                }));
            }
            let entityRoom = EntityRoom.create({
                uuid: room.uuid,
                type: EntityType.room,
                name: room.name || "",
                profile: StorePolyline.create({ vertices: vertices })
            });

            if (room.uid) {
                this._uid2uuidMap.set(room.uid.toString(), entityRoom.uuid);
            }

            ServiceManager.instance.storeService.addEntity(entityRoom);
        }
    }

    private async deserializeFurnitureList(furnitureList: I_SwjFurnitureData[]): Promise<void> {
        for (let i = 0; i < furnitureList.length; i++) {
            let entityFurniture = this.createFurniture(furnitureList[i]);
            ServiceManager.instance.storeService.addEntity(entityFurniture);
        }
    }

    private createFurniture(furniture: I_SwjFurnitureData): IEntityFurniture {
        let figureElement = furniture?._figure_element;
        let uuid = generateUUID();
        if (furniture.uid) {
            this._uid2uuidMap.set(furniture.uid.toString(), uuid);
        }
        let entityFurniture = EntityFurniture.create({
            uuid: uuid,
            type: EntityType.furniture,
            category: figureElement?.category || "",
            subCategory: figureElement?.sub_category || "",
            materialId: figureElement?._matched_material?.modelId || "",
            x: furniture.pos_x || 0,
            y: furniture.pos_y || 0,
            z: furniture.pos_z || 0,
            length: figureElement?.params?.length || 0,
            width: figureElement?.params?.depth || 0,
            height: figureElement?.params?.height || 0,
            normalX: figureElement?._rect_data?.nor.x || 0,
            normalY: figureElement?._rect_data?.nor.y || 0,
            normalZ: figureElement?._rect_data?.nor.z || 0,
        });
        return entityFurniture;
    }

    private async deserializeFurnitureGroupList(furnitureGroupList: I_SwjBaseGroup[]): Promise<void> {
        for (let i = 0; i < furnitureGroupList.length; i++) {
            let furnitureGroup = furnitureGroupList[i];
            let uuid = generateUUID();
            if (furnitureGroup.uid) {
                this._uid2uuidMap.set(furnitureGroup.uid.toString(), uuid);
            }

            let children: IEntityFurniture[] = [];
            let subList = furnitureGroup.sub_list || [];
            for (let sub of subList) {
                let child = this.createFurniture(sub);
                children.push(child);
            }

            let angle = furnitureGroup.rotate_z || 0;
            let nor = new Vector3().copy({ x: Math.sin(angle), y: -Math.cos(angle), z: 0 });

            let entityFurnitureGroup = EntityFurnitureGroup.create({
                uuid: uuid,
                type: EntityType.furnitureGroup,
                category: furnitureGroup.name || "",
                subCategory: furnitureGroup.name || "",
                x: furnitureGroup.pos_x || 0,
                y: furnitureGroup.pos_y || 0,
                z: furnitureGroup.pos_z || 0,
                length: furnitureGroup.length || 0,
                width: furnitureGroup.width || 0,
                height: furnitureGroup.height || 0,
                normalX: nor.x,
                normalY: nor.y,
                normalZ: nor.z,
                children: children,
            });
            ServiceManager.instance.storeService.addEntity(entityFurnitureGroup);
        }
    }

    private async deserializeWindowList(windowList: I_Window[]): Promise<void> {
        for (let i = 0; i < windowList.length; i++) {
            let window = windowList[i];
            let uuid = generateUUID();
            if (window.uid) {
                this._uid2uuidMap.set(window.uid.toString(), uuid);
            }
            let nor = window._nor_data || { x: 0, y: 0, z: 0 };
            let entityWindow = EntityWindow.create({
                uuid: uuid,
                type: EntityType.window,
                eType: window.type || "",
                realType: window.realType || "",
                mirror: window.mirror || 0,
                x: window.pos_x || 0,
                y: window.pos_y || 0,
                length: window.length || 0,
                width: window.width || 0,
                normalX: nor.x || 0,
                normalY: nor.y || 0,
                normalZ: nor.z || 0,
                rotationZ: window.rotate_z || 0,
            });
            ServiceManager.instance.storeService.addEntity(entityWindow);
        }
    }

    private async deserializeDoorList(doorList: I_Door[]): Promise<void> {
        for (let i = 0; i < doorList.length; i++) {
            let door = doorList[i];
            let uuid = generateUUID();
            if (door.uid) {
                this._uid2uuidMap.set(door.uid.toString(), uuid);
            }
            let nor = door._nor_data || { x: 0, y: 0, z: 0 };
            let entityDoor = EntityDoor.create({
                uuid: uuid,
                type: EntityType.door,
                eType: door.type || "",
                realType: door.realType || "",
                mirror: door.mirror || 0,
                x: door.pos_x || 0,
                y: door.pos_y || 0,
                length: door.length || 0,
                width: door.width || 0,
                normalX: nor.x || 0,
                normalY: nor.y || 0,
                normalZ: nor.z || 0,
                rotationZ: door.rotate_z || 0,
                roomTypes: door.room_names || [],
            });
            ServiceManager.instance.storeService.addEntity(entityDoor);
        }
    }

    private async deserializeSeriesList(seriesMap: Record<string, ISeries>): Promise<void> {
        for (let roomUid of Object.keys(seriesMap)) {
            let series = seriesMap[roomUid] as ISeries;
            let storeSeries = StoreSeries.create({
                kgId: series.kgId,
                ruleId: series.ruleId,
                ruleName: series.ruleName,
                seriesStyle: series.seriesStyle,
                seriesKgId: series.seriesKgId,
                status: series.status,
                thumbnail: series.thumbnail,
            });
            let uuid = this._uid2uuidMap.get(roomUid);
            if (!uuid) {
                console.error("series room uid not found", roomUid);
                continue;
            }
            ServiceManager.instance.storeService.addRoomSeries(uuid, storeSeries);
        }
    }


    /**
     * @description 序列化
     * @returns 序列化后的方案内容
     */
    public serialize(): any {
        return null;
    }
}