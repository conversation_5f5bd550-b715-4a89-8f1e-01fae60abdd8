/**
 * @description 实体类型
 * <AUTHOR>
 * @date 2025-06-16
 * @lastEditTime 2025-06-16 11:04:10
 * @lastEditors xuld
 */
export enum EntityType {
    // 基础
    base = "EntityBase",

    // 墙
    wall = "EntityWall",
    // 房间
    room = "EntityRoom",
    // 门
    door = "EntityDoor",
    // 窗户
    window = "EntityWindow",
    // 天花板
    ceiling = "EntityCeiling",
    // 家具
    furniture = "EntityFurniture",
    // 组合家具
    furnitureGroup = "EntityFurnitureGroup",
}

/**
 * @description 非实体的存储类型
 * <AUTHOR>
 * @date 2025-06-17
 * @lastEditTime 2025-06-17 11:04:10
 * @lastEditors xuld
 */
export enum StoreType {
    // 折线
    polyline = "StoreTypePolyline",
    // 点
    point = "StoreTypePoint",
    // 方案
    scheme = "StoreTypeScheme",
    // 样本
    series = "StoreTypeSeries",
}


/**
 * @description 房间实体类型
 */
export enum RoomEntityRealType {
    // 飘窗
    BayWindow = "BayWindow",
    // 栏杆
    Railing = "Railing",
    // 门
    SingleDoor = "SingleDoor",
    DoubleDoor = "DoubleDoor",
    SafetyDoor = "SafetyDoor",
    SlidingDoor = "SlidingDoor",
    PassDoor = "PassDoor",
    DoorHole = "DoorHole"
}