import { Instance, SnapshotIn, SnapshotOut, types } from "mobx-state-tree";

import { StoreType } from "../entity/EntityType";


/**
* @description 折线数据模型
* <AUTHOR>
* @date 2025-06-17
* @lastEditTime 2025-06-17 11:05:25
* @lastEditors xuld
*/
export const StoreScheme = types.model(StoreType.scheme, {
    id: types.optional(types.string, ""),
    name: types.optional(types.string, ""),
    version: types.optional(types.string, ""),
    layoutId: types.optional(types.string, ""),
    hxId: types.optional(types.string, ""),
    wireFrameImageJsonUrl: types.optional(types.string, ""),
}).actions(self => ({
    setId(id: string) {
        self.id = id;
    },
    setName(name: string) {
        self.name = name;
    },
    setVersion(version: string) {
        self.version = version;
    },
    setLayoutId(layoutId: string) {
        self.layoutId = layoutId;
    },
    setWireFrameImageJsonUrl(wireFrameImageJsonUrl: string) {
        self.wireFrameImageJsonUrl = wireFrameImageJsonUrl;
    },
    setHxId(hxId: string) {
        self.hxId = hxId;
    },
    clear() {
        self.id = "";
        self.name = "";
        self.version = "";
        self.layoutId = "";
        self.wireFrameImageJsonUrl = "";
    },
}));

export interface IStoreScheme extends Instance<typeof StoreScheme> { }
export interface IStoreSchemeSnapshotIn extends SnapshotIn<typeof StoreScheme> { }
export interface IStoreSchemeSnapshotOut extends SnapshotOut<typeof StoreScheme> { }