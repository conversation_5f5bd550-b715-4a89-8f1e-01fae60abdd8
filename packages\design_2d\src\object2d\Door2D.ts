import { IEntityDoor } from "@layoutai/design_domain";

import { g_figure_alias_dict } from "../draw/FigureImagePaths";
import { TPainter } from "../draw/TPainter";
import { Object2DBase } from "./Object2DBase";


/**
* @description 窗户 2D 对象
*/
export class Door2D extends Object2DBase {
    // 是否显示轮廓
    protected _spotLight: boolean = false;

    public get door(): IEntityDoor {
        return this.entity as IEntityDoor;
    }

    public update(): any | undefined {
        if (!this.door) {
            console.error("门实体不存在，无法更新");
            return undefined;
        }

        return this.door;
    }

    public render(painter: TPainter): void {

        let rect = this.door.rect;
        let line_color: string = "#777777";

        painter.strokeStyle = "#000";

        painter.fillStyle = this._drawParam.fillStyle;

        painter._context.lineWidth = this._drawParam.lineWidth;

        painter._style_mapping["black"] = line_color;

        let category = this.door.category;
        let subCategory = this.door.subCategory;
        let candidate_labels = [g_figure_alias_dict[category], subCategory, category];
        painter.drawFigureRect(rect, category, candidate_labels);

        painter.fillStyle = "#000";

        if (this._spotLight) {
            painter.drawEdges(rect.edges, 0, "#f00");
        }
    }
} 