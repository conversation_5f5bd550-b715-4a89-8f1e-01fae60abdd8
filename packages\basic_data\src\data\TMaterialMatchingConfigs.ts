
export class TMaterialMatchingConfigs
{
    static roomTypesSupportCeilling:Set<string> = new Set(["客餐厅", "卧室", "书房", "卫生间", "厨房","阳台"]);

    static roomTypesOrders : string[] = ["客餐厅","厨房","卧室","书房","卫生间","阳台"];
    static roomType2DoorModellocMap: Map<string, string> = new Map([["客餐厅", "入户门"], ["卧室", "卧室门"], ["书房", "书房门"], ["卫生间", "卫生间门"], ["厨房", "厨房门"], ["阳台", "阳台门"]]);

    static _backto_wall_modelLoc_list = ["衣柜", "浴室柜", "餐边柜", "玄关柜",
    "电视柜", "梳妆台", "沙发", "洗衣机柜", "书桌", "书柜", "床头柜", "梳妆台", "花洒", "马桶", "毛巾架", "床", "窗帘", "电视",
     "背景墙", "沙发背景墙", "电视背景墙","墙饰","餐厅墙饰","客厅墙饰", "左收口板", "右收口板", "地柜", "吊柜", "沙发组合", "床组合", "榻榻米组合", "书桌组合"];
    static _sideto_wall_modelLoc_list: Set<string> = new Set(["衣柜", "书柜", "玄关柜"]);

     static _frontto_wall_modelLoc_list:  Set<string> = new Set(["衣柜", "书柜", "玄关柜"]);
     static _align_top_modelLoc_list: Set<string> = new Set(["客厅主灯", "主灯", "床头吊灯", "筒灯","吸顶灯"]);
    static _grounded_modelLoc_list: Set<string> = new Set(["落地灯"]);
    static _storey_height = 2800;
    static _default_ceiling_height = 200;
    static  _bathroom_cabinet_table_intall_z = 800;
    static  _bathroom_cabinet_default_offland_height = 400;
    
    static lengthWidthHeightScaleableModelLocs = ["入户门","卧室门","书房门","卫生间门","厨房门","阳台门","其它门","一字窗","飘窗","推拉门"];
    static lengthWidthScaleableModelLocs = ["地毯", "淋浴房"];
    static lengthHeightScaleableModelLocs = ["窗帘", "背景墙", "沙发背景墙", "电视背景墙"];
    /**
     *  用旋转来处理镜像的模型位
     */
    static  NoMirrorWithRotationModelLocs = ["钻石形淋浴房","矩形淋浴房"];
    static lengthScaleableModelLocs = ["淋浴房"];
    static modelLoc2MaxWidthMap = new Map<string, number>([["窗帘", 200]]);

    static  _ignoreCategories = ["相机","人物","Default"];
}