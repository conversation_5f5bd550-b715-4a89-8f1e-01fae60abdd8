import { I_DesignMaterialInfo, I_MaterialMatchingItem } from "@layoutai/basic_data";

import { HttpRequest } from "../request/HttpRequest";
import { TSize } from "./series/TSizeRange";
import { TSerialSizeRangeDB } from "./series/TSeriesSizeRangeDB";


/**
* @description 素材服务
* <AUTHOR>
* @date 2025-06-27
* @lastEditTime 2025-06-27 11:32:19
* @lastEditors xuld
*/
export class MaterialService {

    static async getDesignMaterialInfoByIds(materialIds: string[]): Promise<I_DesignMaterialInfo[]> {
        let postReqBody = {
            materialIds: materialIds.join(",")
        }

        try {
            const res = await HttpRequest.openApiRequest({
                method: 'post',
                url: `/api/sdapi/designmaterial/getDesignMaterialByIdsWithOutPlaceheights`,
                data: {
                    ...postReqBody,
                },
                timeout: 60000,
            });

            if (res.success == false) {
                return [];
            }
            const materialInfos = res.result?.result as I_DesignMaterialInfo[] || [];
            return materialInfos;
        } catch (error) {
            console.log(error);
            return [];
        }
    }

    private static getMatchItemFromRecord(record: any): I_MaterialMatchingItem {
        let matchingItem = {
            name: record.name,
            type: record.type,
            modelLoc: record.modelLoc,
            length: record.length,
            width: record.width,
            height: record.height,
            originalLength: record.originalLength,
            originalWidth: record.originalWidth,
            originalHeight: record.originalHeight,
            modelId: record.modelId,
            imageUrl: record.imageUrl,
            targetRotation: { x: 0, y: 0, z: record.rotateZ },
            targetPosition: { x: record.x, y: record.y, z: record.z },
            topViewImage: record.topViewImage
        } as I_MaterialMatchingItem;
        return matchingItem;
    }


    static async getGroupMaterialDetail(materialId: string): Promise<I_MaterialMatchingItem[]> {
        let memberMaterials: I_MaterialMatchingItem[] = [];

        try {
            let postReqBody = {
                modelId: materialId
            };

            const res = await HttpRequest.magiccubeDpAiWebRequest({
                method: 'post',
                url: `/dp-ai-web/getGroupDetail`,
                data: {
                    ...postReqBody,
                },
                timeout: 60000,
            });

            if (res.success == false || res.data == null) {
                return [];
            }

            if (res.data.records != null) {
                for (let record of res.data.records) {
                    let matchingItem: I_MaterialMatchingItem = this.getMatchItemFromRecord(record);
                    memberMaterials.push(matchingItem);
                }
            }
        } catch (e) {
        }

        let memberMaterialsCopy: I_MaterialMatchingItem[] = [];
        if (memberMaterials.length > 0) {
            memberMaterials.forEach((mm) => {
                memberMaterialsCopy.push(this.cloneMaterialMatchingItem(mm));
            });
        }

        return memberMaterialsCopy;
    }

    public static cloneMaterialMatchingItem(mm: I_MaterialMatchingItem): I_MaterialMatchingItem {
        return {
            modelLoc: mm.modelLoc,
            length: mm.length,
            width: mm.width,
            height: mm.height,
            modelId: mm.modelId,
            imageUrl: mm.imageUrl,
            originalLength: mm.originalLength,
            originalWidth: mm.originalWidth,
            originalHeight: mm.originalHeight,
            targetSize: { width: mm.width, length: mm.length, height: mm.height },
            targetRotation: { x: 0, y: 0, z: mm.targetRotation.z },
            targetPosition: { x: mm.targetPosition.x, y: mm.targetPosition.y, z: mm.targetPosition.z },
            topViewImage: mm.topViewImage
        } as I_MaterialMatchingItem;
    }

    public static getDefaultMaterialId(rect: any, subCategory: string){
        let size = new TSize(rect.w - 0.1, rect.h - 0.1, 0);
        let res = TSerialSizeRangeDB.QueryDefaultModelIds(subCategory, size);
        if (!res?.length) {
            console.error("default materialId is null", subCategory);
            return;
        }
        return res[0];
    }
}