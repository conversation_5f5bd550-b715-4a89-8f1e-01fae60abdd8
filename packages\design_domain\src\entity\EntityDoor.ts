import { Instance, SnapshotIn, SnapshotOut, types } from "mobx-state-tree";
import { Vector3 } from "three";
import { ZRect } from "z_polygon";
import { EntityBase } from "./EntityBase";
import { EntityType } from "./EntityType";
import { I_DesignMaterialInfo, IType2UITypeDict } from "@layoutai/basic_data";
import { MaterialService } from "../service/MaterialService";

/**
 * @description 门实体
 */
export const EntityDoor = types
    .compose(
        EntityBase,
        types.model({
            // 是否可见，默认为 true 可见
            visible: types.optional(types.boolean, true),
            // 类型
            eType: types.string,
            realType: types.string,
            // 镜像
            mirror: types.number,
            // 位置
            x: types.number,
            y: types.number,
            z: types.optional(types.number, 0),
            // 法线
            normalX: types.number,
            normalY: types.number,
            normalZ: types.number,
            // 旋转
            rotationZ: types.number,
            // 尺寸
            length: types.number,
            width: types.number,
            height: types.optional(types.number, 2200),
            // 房间
            roomTypes: types.array(types.string),
        })
    )
    .named(EntityType.door)
    .props({
        type: EntityType.door,
    })
    .actions((self) => ({
        // 设置位置
        setX(x: number) {
            self.x = x;
        },
        setY(y: number) {
            self.y = y;
        },
        setZ(z: number) {
            self.z = z;
        },
        setPosition(x: number, y: number, z: number) {
            self.x = x;
            self.y = y;
            self.z = z;
        },
        // 设置法线
        setNormal(normalX: number, normalY: number, normalZ: number) {
            self.normalX = normalX;
            self.normalY = normalY;
            self.normalZ = normalZ;
        },
        // 设置尺寸
        setLength(length: number) {
            self.length = length;
        },
        setWidth(width: number) {
            self.width = width;
        },
        setHeight(height: number) {
            self.height = height;
        },
        setSize(length: number, width: number, height: number) {
            self.length = length;
            self.width = width;
            self.height = height;
        },
        // 设置是否可见
        setVisible(visible: boolean) {
            self.visible = visible;
        },
    }))
    .views((self) => ({
        get thickness() {
            return self.width;
        },
        get category() {
            let room_types = self.roomTypes;
            room_types.sort((a, b) => TMaterialMatchingConfigs.roomTypesOrders.indexOf(b) - TMaterialMatchingConfigs.roomTypesOrders.indexOf(a));
            let first_room_type = room_types[0] || "";
            return IType2UITypeDict[self.realType];
        },
        get subCategory() {
            return IType2UITypeDict[self.realType || self.eType];
        },
        get rect() {
            const r = new ZRect(self.length, self.width);
            r.nor = new Vector3(self.normalX, self.normalY, self.normalZ);
            r.rect_center_3d = new Vector3(self.x, self.y, self.z);
            r.updateRect();
            return r;
        },
        get materialId() {
            return MaterialService.getDefaultMaterialId(this.rect, this.category);
        },
        async materialInfo(): Promise<I_DesignMaterialInfo | undefined> {
            if (!this.materialId) {
                return;
            }
            let dvoList = await MaterialService.getDesignMaterialInfoByIds([this.materialId]);
            if (!dvoList.length) {
                return;
            }
            return dvoList[0];
        }
    }));

export interface IEntityDoor extends Instance<typeof EntityDoor> {}
export interface IEntityDoorSnapshotIn extends SnapshotIn<typeof EntityDoor> {}
export interface IEntityWDoorSnapshotOut extends SnapshotOut<typeof EntityDoor> {}