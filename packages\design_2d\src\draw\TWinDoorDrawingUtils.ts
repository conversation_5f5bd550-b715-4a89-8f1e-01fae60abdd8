import { Vector3 } from "three";
import { TPainter } from "./TPainter";
import { IRoomEntityRealType, IRoomEntityType } from "@layoutai/basic_data";
import { ZPolygon, ZRect } from "z_polygon";
// import { I_EntityDrawingState } from "../TBaseEntity";

export interface I_EntityDrawingState {
    is_moving?: boolean, is_selected?: boolean, is_hovered?: boolean,
    is_draw_figure?: boolean, is_draw_texture?: boolean,
    is_show_room_id?: boolean, is_animation?: boolean, is_draw_roomname?: boolean,
    globalAlpha?: number, draw_decoration?: boolean,
    is_mobile?: boolean,
    is_draw_outline?: boolean,
}
export class TWinDoorDrawingUtils {
    
    /**
     * 主绘制方法：绘制门窗矩形
     */
    public static drawWinDoorRect(painter: TPainter, rect: ZRect, type: IRoomEntityType, realType: IRoomEntityRealType, options: I_EntityDrawingState) {
        let fillstyle = painter.fillStyle as string;
        let strokestyle = painter.strokeStyle;
        
        painter.fillStyle = "#fff";
        painter.fillPolygons([rect], 1.);
        if (options.is_selected) {
            painter.fillStyle = fillstyle = "#E6F6FF"
            painter.strokeStyle = strokestyle = "#147FFA";
        }
        else if (options.is_hovered) {
            painter.fillStyle = fillstyle = "#fff"
            painter.strokeStyle = strokestyle = "#3D9EFF";
        }
        else if (options.is_draw_figure) {
            painter.fillStyle = fillstyle = "#fff"
            painter.strokeStyle = strokestyle = "#282828";
        }

        if (type === "Door") {
            if (realType === "SingleDoor") {
                this.drawSingleDoor(painter, rect, options, fillstyle, strokestyle);
            }
            else if (realType === "DoubleDoor") {
                this.drawDoubleDoor(painter, rect, options, fillstyle, strokestyle);
            }
            else if (realType === "SlidingDoor") {
                this.drawSlidingDoor(painter, rect, options);
            }
        }
        else if (type === "Window") {
            if (realType === "BayWindow") {
                this.drawBayWindow(painter, rect, options, fillstyle, strokestyle);
            }
            else {
                this.drawOneWindow(painter, rect, options);
            }
        }
    }

    /**
     * 绘制单开门
     */
    private static drawSingleDoor(painter: TPainter, rect: ZRect, options: I_EntityDrawingState, fillstyle: string, strokestyle: string) {
        let door_board_rect = new ZRect(50, rect.w);
        door_board_rect.nor = rect.nor.clone().negate();
        door_board_rect.back_center = rect.unproject({ x: -rect.w / 2 + door_board_rect.w / 2, y: 0 });
        door_board_rect.updateRect();

        // 获取单开门的扇形区域
        let arc_poly = this.getSingleDoorSectorArea(rect);

        if (options.is_selected) {
            painter.fillStyle = "#147FFA0A";
            painter.strokeStyle = "#8FCEFF";
        }
        else if (options.is_hovered) {
            painter.fillStyle = "#FFFFFF33";
            painter.strokeStyle = "#BCBEC2";
        }
        else if (options.is_draw_figure) {
            painter.fillStyle = "#FFFFFF33";
            painter.strokeStyle = "#BCBEC2";
        }
        
        painter.strokePolygons([arc_poly]);
        painter.fillPolygon(arc_poly, 1.);

        painter.strokeStyle = strokestyle;
        painter.fillStyle = fillstyle;
        painter.strokePolygons([rect]);
        painter.fillPolygon(rect, 1.);
        painter.fillPolygon(door_board_rect, 1.);

        painter.strokePolygons([door_board_rect]);
    }

    /**
     * 绘制双开门
     */
    private static drawDoubleDoor(painter: TPainter, rect: ZRect, options: I_EntityDrawingState, fillstyle: string, strokestyle: string) {
        let ww = rect.w / 2;

        let door_board_rect0 = new ZRect(50, ww);
        door_board_rect0.nor = rect.nor.clone().negate();
        door_board_rect0.back_center = rect.unproject({ x: -rect.w / 2 + door_board_rect0.w / 2, y: 0 });
        door_board_rect0.updateRect();

        let door_board_rect1 = door_board_rect0.clone();
        door_board_rect1.back_center = rect.unproject({ x: rect.w / 2 - door_board_rect0.w / 2, y: 0 });
        door_board_rect1.updateRect();

        // 获取双开门的扇形区域
        const [arc_poly0, arc_poly1] = this.getDoubleDoorSectorAreas(rect);

        if (options.is_selected) {
            painter.fillStyle = "#147FFA0A";
            painter.strokeStyle = "#8FCEFF";
        }
        else if (options.is_hovered) {
            painter.fillStyle = "#FFFFFF33";
            painter.strokeStyle = "#BCBEC2";
        }
        else if (options.is_draw_figure) {
            painter.fillStyle = "#FFFFFF33";
            painter.strokeStyle = "#BCBEC2";
        }
        
        painter.strokePolygons([arc_poly0]);
        painter.fillPolygon(arc_poly0, 1.);
        painter.strokePolygons([arc_poly1]);
        painter.fillPolygon(arc_poly1, 1.);

        painter.strokeStyle = strokestyle;
        painter.fillStyle = fillstyle;
        painter.strokePolygons([rect]);
        painter.fillPolygon(rect, 1.);
        painter.fillPolygon(door_board_rect0, 1.);
        painter.fillPolygon(door_board_rect1, 1.);
        painter.strokePolygons([door_board_rect0, door_board_rect1]);
    }

    /**
     * 绘制推拉门
     */
    private static drawSlidingDoor(painter: TPainter, rect: ZRect, options: I_EntityDrawingState) {
        let pattern_name = "推拉门";
        if (options.is_selected) {
            pattern_name += "-select";
        }
        else if (options.is_hovered) {
            pattern_name += "-hover";
        }
        painter.drawSvgPatternInRect(pattern_name, rect);
    }

    /**
     * 绘制一字窗
     */
    private static drawOneWindow(painter: TPainter, rect: ZRect, options: I_EntityDrawingState) {
        let pattern_name = "一字窗";
        if (options.is_selected) {
            pattern_name += "-select";
        }
        else if (options.is_hovered) {
            pattern_name += "-hover";
        }
        painter.drawSvgPatternInRect(pattern_name, rect);
    }

    /**
     * 绘制飘窗
     */
    private static drawBayWindow(painter: TPainter, rect: ZRect, options: I_EntityDrawingState, fillstyle: string, strokestyle: string) {
        if (options.is_selected) {
            painter.fillStyle = "#E6F6FF";
            painter.strokeStyle = "#E6F6FF";
        }
        else if (options.is_hovered) {
            painter.fillStyle = "#BCBEC2";
            painter.strokeStyle = "#BCBEC2";
        }
        else if (options.is_draw_figure) {
            painter.fillStyle = "#BCBEC2";
            painter.strokeStyle = "#BCBEC2";
        }

        painter.fillPolygons([rect], 1.);

        painter.strokeStyle = strokestyle;
        painter.fillStyle = "#FFFFFF";
        let thickness = 120;

        let t_rect = rect.clone();

        t_rect._w += thickness * 2;
        t_rect._h += thickness;
        t_rect.updateRect();
        t_rect.reOrderByOrientation(true);

        let s_rect = rect.clone();
        s_rect.reOrderByOrientation(true);

        let s_polys = [s_rect];

        let w_rect = rect.clone();
        w_rect._w += thickness * 2.5;
        w_rect._h = 150;
        w_rect.updateRect();
        w_rect.reOrderByOrientation(true);
        s_polys.push(w_rect);

        let child_polys = t_rect.substract_polygons(s_polys);

        if (child_polys[0]) {
            painter.fillPolygon(child_polys[0], 1.);
            painter.strokePolygons(child_polys);
        }
    }

    /**
     * 绘制门洞
     */
    public static drawDoorHole(painter: TPainter, rect: ZRect, options: I_EntityDrawingState) {
        let fillstyle = painter.fillStyle;
        let strokestyle = painter.strokeStyle;
        if (options.is_selected) {
            painter.fillStyle = "#E6F6FF";
            painter.strokeStyle = "#147FFA";
        }
        else if (options.is_hovered) {
            painter.fillStyle = "#FFFFFF";
            painter.strokeStyle = "#3D9EFF";
        }
        else if (options.is_draw_figure) {
            painter.fillStyle = "#FFFFFF";
            painter.strokeStyle = "#282828";
        }
        
        // 门洞就是中规中矩的矩形 中间白色
        painter.fillPolygon(rect, 1.);
        painter.strokePolygons([rect]);
        painter.fillStyle = fillstyle;
        painter.strokeStyle = strokestyle;
    }
    
    /**
     * 绘制栏杆
     */
    public static drawRailing(painter: TPainter, rect: ZRect, options: I_EntityDrawingState) {
        let fillstyle = painter.fillStyle;
        let strokestyle = painter.strokeStyle;
        let line_color = "#282828";
        if (options.is_selected) {
            painter.fillStyle = "#E6F6FF";
            line_color = "#147FFA";
        }
        else if (options.is_hovered) {
            painter.fillStyle = "#FFFFFF";
            line_color = "#3D9EFF";
        }
        else if (options.is_draw_figure) {
            painter.fillStyle = "#FFFFFF";
            line_color = "#282828";
        }
        painter.strokeStyle = line_color;
        
        // 绘制基础矩形
        painter.fillPolygon(rect, 1.);
        painter.strokePolygons([rect]);
        
        // 栏杆是中间多两条白线更靠中心一点
        const lineWidth = painter._context.lineWidth;
        painter._context.lineWidth = 2;
        
        // 绘制两条栏杆线
        const railGap = rect.h / 10;
        
        // 第一条栏杆线
        const rail1Start = rect.unproject({ x: -rect.w/2, y: -railGap });
        const rail1End = rect.unproject({ x: rect.w/2, y: -railGap });
        
        // 第二条栏杆线
        const rail2Start = rect.unproject({ x: -rect.w/2, y: railGap });
        const rail2End = rect.unproject({ x: rect.w/2, y: railGap });
        
        // 使用与边框相同的颜色
        painter.drawLineSegment(rail1Start, rail1End, line_color);
        painter.drawLineSegment(rail2Start, rail2End, line_color);
        
        // 恢复线宽和样式
        painter._context.lineWidth = lineWidth;
        painter.fillStyle = fillstyle;
        painter.strokeStyle = strokestyle;
    }
    
    /**
     * 绘制子母门
     */
    public static drawSafetyDoor(painter: TPainter, rect: ZRect, options: I_EntityDrawingState) {
        let fillstyle = painter.fillStyle;
        let strokestyle = painter.strokeStyle;
        
        // 修改门扇宽度比例 左侧占1/3 右侧占2/3
        let leftWidth = rect.w * (1/3);
        let rightWidth = rect.w * (2/3);

        // 左侧门板
        let door_board_rect0 = new ZRect(50, leftWidth);
        door_board_rect0.nor = rect.nor.clone().negate();
        door_board_rect0.back_center = rect.unproject({ x: -rect.w / 2 + door_board_rect0.w / 2, y: 0 });
        door_board_rect0.updateRect();

        // 右侧门板
        let door_board_rect1 = new ZRect(50, rightWidth);
        door_board_rect1.nor = rect.nor.clone().negate();
        door_board_rect1.back_center = rect.unproject({ x: rect.w / 2 - door_board_rect1.w / 2, y: 0 });
        door_board_rect1.updateRect();

        // 使用通用方法获取子母门的扇形区域
        const [arc_poly0, arc_poly1] = this.getSafetyDoorSectorAreas(rect);
        
        // 设置颜色
        let fill_color = "#FFFFFF";
        let line_color = "#BCBEC2";
        if (options.is_selected) {
            fill_color = "#E6F6FF";
            line_color = "#147FFA";
        }
        else if (options.is_hovered) {
            fill_color = "#FFFFFF";
            line_color = "#3D9EFF";
        }
        else if (options.is_draw_figure) {
            fill_color = "#FFFFFF";
            line_color = "#282828";
        }
        painter.strokeStyle = line_color;
        
        painter.fillStyle = '#FFFFFF33';
        // 绘制扇形 需要透明度
        painter.strokePolygons([arc_poly0]);
        painter.fillPolygon(arc_poly0, 1.);
        painter.strokePolygons([arc_poly1]);
        painter.fillPolygon(arc_poly1, 1.);
        painter.fillStyle = fill_color;

        // 绘制门框和门板
        painter.strokePolygons([rect]);
        painter.fillPolygon(rect, 1.);
        painter.fillPolygon(door_board_rect0, 1.);
        painter.fillPolygon(door_board_rect1, 1.);
        painter.strokePolygons([door_board_rect0, door_board_rect1]);

        // 恢复样式
        painter.fillStyle = fillstyle;
        painter.strokeStyle = strokestyle;
    }
    
    /**
     * 绘制垭口
     */
    public static drawPassDoor(painter: TPainter, rect: ZRect, options: I_EntityDrawingState) {
        let fillstyle = painter.fillStyle;
        let strokestyle = painter.strokeStyle;
        let line_color = "#282828";
        
        if (options.is_selected) {
            painter.fillStyle = "#E6F6FF";
            line_color = "#147FFA";
        }
        else if (options.is_hovered) {
            painter.fillStyle = "#FFFFFF";
            line_color = "#3D9EFF";
        }
        else if (options.is_draw_figure) {
            painter.fillStyle = "#FFFFFF";
            line_color = "#282828";
        }
        painter.strokeStyle = line_color;
        
        // 绘制基础门洞矩形
        painter.fillPolygon(rect, 1);
        painter.strokePolygons([rect]);
        
        // 垭口是门洞的基础上两侧各多加一个竖线
        const lineGap = rect.w * 0.03; // 距离边缘3%的位置
        const lineWidth = painter._context.lineWidth;
        painter._context.lineWidth = 2;
        
        // 左侧竖线
        const leftLineStart = rect.unproject({ x: -rect.w/2 + lineGap, y: -rect.h/2 });
        const leftLineEnd = rect.unproject({ x: -rect.w/2 + lineGap, y: rect.h/2 });
        
        // 右侧竖线
        const rightLineStart = rect.unproject({ x: rect.w/2 - lineGap, y: -rect.h/2 });
        const rightLineEnd = rect.unproject({ x: rect.w/2 - lineGap, y: rect.h/2 });
        
        // 使用与边框相同的颜色
        painter.drawLineSegment(leftLineStart, leftLineEnd, line_color);
        painter.drawLineSegment(rightLineStart, rightLineEnd, line_color);
        
        // 恢复线宽和样式
        painter._context.lineWidth = lineWidth;
        painter.fillStyle = fillstyle;
        painter.strokeStyle = strokestyle;
    }


    /**
     * 获取单开门扇形区域
     */
    public static getSingleDoorSectorArea(rect: ZRect, num: number = 20): ZPolygon {
        let arc_poly_points: Vector3[] = [];
        arc_poly_points.push(rect.unproject({ x: rect.w / 2, y: 0 }));

        for (let i = 0; i <= num; i++) {
            let angle = Math.PI / 2. * i / num;
            let xx = Math.cos(angle) * rect.w - rect.w / 2;
            let yy = Math.sin(angle) * rect.w;

            arc_poly_points.push(rect.unproject({ x: xx, y: -yy }));
        }

        arc_poly_points.push(rect.unproject({ x: -rect.w / 2, y: 0 }));

        let arc_poly = new ZPolygon();
        arc_poly.initByVertices(arc_poly_points);
        return arc_poly;
    }

    /**
     * 获取双开门扇形区域，返回左右两个扇形
     */
    public static getDoubleDoorSectorAreas(rect: ZRect, num: number = 20): [ZPolygon, ZPolygon] {
        let ww = rect.w / 2;  // 每扇门的宽度

        // 创建左侧扇形点集
        let arc_poly_points_left: Vector3[] = [];
        arc_poly_points_left.push(rect.unproject({ x: -rect.w / 2, y: 0 }));

        for (let i = 0; i <= num; i++) {
            let angle = Math.PI / 2. * i / num;
            let xx = Math.cos(angle) * ww - rect.w / 2;
            let yy = Math.sin(angle) * ww;

            arc_poly_points_left.push(rect.unproject({ x: xx, y: -yy }));
        }

        arc_poly_points_left.push(rect.unproject({ x: -ww, y: 0 }));

        // 创建右侧扇形点集
        let arc_poly_points_right: Vector3[] = [];
        arc_poly_points_right.push(rect.unproject({ x: rect.w / 2, y: 0 }));

        for (let i = 0; i <= num; i++) {
            let angle = Math.PI / 2. * i / num;
            let xx = Math.cos(angle) * ww;
            let yy = Math.sin(angle) * ww;

            arc_poly_points_right.push(rect.unproject({ x: rect.w / 2 - xx, y: -yy }));
        }

        arc_poly_points_right.push(rect.unproject({ x: ww, y: 0 }));

        // 创建扇形多边形
        let arc_poly_left = new ZPolygon();
        arc_poly_left.initByVertices(arc_poly_points_left);
        
        let arc_poly_right = new ZPolygon();
        arc_poly_right.initByVertices(arc_poly_points_right);

        return [arc_poly_left, arc_poly_right];
    }

    /**
     * 获取子母门扇形区域，返回左右两个扇形
     */
    public static getSafetyDoorSectorAreas(rect: ZRect, num: number = 20): [ZPolygon, ZPolygon] {
        // 修改门扇宽度比例 左侧占1/3 右侧占2/3
        let leftWidth = rect.w * (1/3);
        let rightWidth = rect.w * (2/3);

        // 创建左侧扇形点集（小门扇）
        let arc_poly_points_left: Vector3[] = [];
        arc_poly_points_left.push(rect.unproject({ x: -rect.w / 2, y: 0 }));

        for (let i = 0; i <= num; i++) {
            let angle = Math.PI / 2. * i / num;
            let xx = Math.cos(angle) * leftWidth - rect.w / 2;
            let yy = Math.sin(angle) * leftWidth;

            arc_poly_points_left.push(rect.unproject({ x: xx, y: -yy }));
        }

        arc_poly_points_left.push(rect.unproject({ x: -rect.w / 2, y: 0 }));

        // 创建右侧扇形点集（大门扇）
        let arc_poly_points_right: Vector3[] = [];
        arc_poly_points_right.push(rect.unproject({ x: rect.w / 2, y: 0 }));

        for (let i = 0; i <= num; i++) {
            let angle = Math.PI / 2. * i / num;
            let xx = Math.cos(angle) * rightWidth;
            let yy = Math.sin(angle) * rightWidth;

            arc_poly_points_right.push(rect.unproject({ x: rect.w / 2 - xx, y: -yy }));
        }

        arc_poly_points_right.push(rect.unproject({ x: rect.w / 2, y: 0 }));

        // 创建扇形多边形
        let arc_poly_left = new ZPolygon();
        arc_poly_left.initByVertices(arc_poly_points_left);
        
        let arc_poly_right = new ZPolygon();
        arc_poly_right.initByVertices(arc_poly_points_right);

        return [arc_poly_left, arc_poly_right];
    }
}