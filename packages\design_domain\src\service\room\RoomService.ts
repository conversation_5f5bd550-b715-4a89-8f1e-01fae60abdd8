import { Instance } from "mobx-state-tree";
import { ZPolygon } from "z_polygon";

import { EntityRoom, IEntityRoom } from "../../entity/EntityRoom";
import { EntityType } from "../../entity/EntityType";
import { EntityWall } from "../../entity/EntityWall";
import { IStorePoint3, StorePoint3 } from "../../store/StorePoint3";
import { StorePolyline } from "../../store/StorePolyline";
import { StoreService } from "../StoreService";
import { EntityServicePriority } from "../EntityServicePriority";
import { IEntityServiceObserver } from "../IEntityServiceObserver";
import { ServiceManager } from "../ServiceManager";
import { TRoomShape } from "./TRoomShape";


/**
* @description 房间服务
* <AUTHOR>
* @date 2025-06-16
* @lastEditTime 2025-06-16 17:31:43
* @lastEditors xuld
*/
export class RoomService implements IEntityServiceObserver {

    private _out_border_polygons: ZPolygon[] = [];

    public getPriority(): number {
        return EntityServicePriority.RoomService;
    }

    private get _entityService(): StoreService {
        return ServiceManager.instance.storeService;
    }

    /**
     * @description 实体添加回调
     * @param entity 实体
     */
    public async onEntityAdded(uuid: string): Promise<void> {
        let entity = this._entityService.getEntity(uuid);
        if (!entity) {
            return;
        }
        switch (entity.type) {
            case EntityType.wall:
                let wall = entity as Instance<typeof EntityWall>;
                this.onAddWall(wall);
                break;
            default:
                break;
        }
    }

    /**
     * @description 实体移除前回调
     * @param uuid 实体uuid
     */
    public async beforeEntityRemoved(uuid: string): Promise<void> {
        let entity = this._entityService.getEntity(uuid);
        if (!entity) {
            return;
        }
        switch (entity.type) {
            case EntityType.wall:
                let wall = entity as Instance<typeof EntityWall>;
                this.beforeRemoveWall(wall);
                break;
            default:
                break;
        }
    }

    /**
     * @description 实体移除前回调
     * @param wall 墙
     */
    private beforeRemoveWall(wall: Instance<typeof EntityWall>): void {
        console.log("onRemoveWall", wall.uuid);
    }

    /**
     * @description 实体移除回调
     * @param entity 实体
     */
    public async afterEntityRemoved(uuid: string): Promise<void> {
        console.log("afterEntityRemoved", uuid);
        this._computeRoomPolysByWall();
    }

    /**
     * @description 清空回调
     */
    public async onClearEntity(): Promise<void> {
        console.log("onClearEntity");
    }

    private onAddWall(wall: Instance<typeof EntityWall>): void {
        // this._computeRoomPolysByWall();
    }

    /**
     * @description 根据墙体计算房间多边形
     * @param clean_old 是否清空原有房间实体列表
     * @param use_overlap 是否使用重叠度匹配策略(true:使用最大重叠度匹配, false:使用最小距离匹配)
     */
    private _computeRoomPolysByWall(clean_old: boolean = true, use_overlap: boolean = true) {
        let wall_entities = this._entityService.getEntitiesByType(EntityType.wall) as Instance<typeof EntityWall>[];
        let wallProfiles: ZPolygon[] = [];
        let extend_len = 1.0;
        for (let wall of wall_entities) {
            let rect = wall.rect.clone();
            let r_center = rect.rect_center;
            rect._w += extend_len;
            rect._h += extend_len;
            rect.rect_center = r_center;
            rect.reOrderByOrientation(true);
            wallProfiles.push(rect);
        }

        if (wallProfiles.length == 0) return;
        let unionPolygons = wallProfiles[0].union_polygons(wallProfiles);

        for (let i = 0; i < unionPolygons.length; i++) {
            let poly = unionPolygons[i];
            poly._attached_elements['main_area'] = 99999;

            if (poly.orientation_z_nor.z > 0) {
                continue;
            }
            let main_rect = TRoomShape.computeMaxRectBySplitShape(poly);
            poly._attached_elements['main_rect'] = main_rect;
            poly._attached_elements['main_area'] = main_rect.w / 10000 * main_rect.h / 1000;
        }

        unionPolygons.sort((a, b) => b._attached_elements['main_area'] - a._attached_elements['main_area'])
        let outerPolygons: ZPolygon[] = [];
        let innerRoomPolys: ZPolygon[] = [];

        for (let poly of unionPolygons) {
            if (poly.orientation_z_nor.z > 0) {
                poly.expandPolygon(-extend_len / 2);
                outerPolygons.push(poly);
                continue;
            }
            else {
                poly.expandPolygon(extend_len / 2);
                innerRoomPolys.push(poly);
            }
        }

        let preRoomEntities: IEntityRoom[] = this._entityService.getEntitiesByType(EntityType.room) as IEntityRoom[];
        preRoomEntities.sort((a, b) => b.area - a.area);
        let newUsed: Map<string, boolean> = new Map();

        let updated_polys: ZPolygon[] = [];
        preRoomEntities.forEach(roomEntity => {
            let originPoly = roomEntity.roomPoly;
            let targetPoly: ZPolygon | undefined;
            let minDist = 100000;
            let maxOverlap = 0;

            // 合并两种匹配策略的循环
            for (let poly of innerRoomPolys) {
                if (updated_polys.includes(poly)) continue;
                let res = poly.comparePolyDistance(originPoly);
                if (use_overlap) {
                    // 使用最大重叠度策略
                    if (res.overlap_length > maxOverlap) {
                        maxOverlap = res.overlap_length;
                        targetPoly = poly;
                    }
                } else {
                    // 使用最小距离策略
                    if (res.sum < minDist) {
                        minDist = res.sum;
                        targetPoly = poly;
                    }
                }
            }

            // 如果找到匹配的多边形,更新房间实体
            if (targetPoly) {
                updated_polys.push(targetPoly);
                roomEntity.updateByPoint(targetPoly.positions);

                if (clean_old) {
                    newUsed.set(roomEntity.uuid, true);
                }
            }
        });

        if (clean_old) {
            // 清理不在的旧房间实体
            preRoomEntities.forEach(roomEntity => {
                if (!newUsed.has(roomEntity.uuid)) {
                    this._entityService.removeEntity(roomEntity.uuid);
                }
            });
        }

        for (let poly of innerRoomPolys) {
            if (!updated_polys.includes(poly)) {
                let vertices: IStorePoint3[] = [];
                poly.vertices.forEach(v => {
                    vertices.push(StorePoint3.create({
                        x: v.pos.x,
                        y: v.pos.y,
                        z: v.pos.z,
                    }));
                });
                let profile = StorePolyline.create({
                    vertices: vertices,
                    isClosed: true
                });
                let target_entity = EntityRoom.create({
                    name: "卧室",
                    profile: profile
                });
                this._entityService.addEntity(target_entity);
            }
        }

        for (let poly of outerPolygons) {
            for (let edge of poly.edges) {
                for (let wall of wall_entities) {
                    let rect = wall.rect;
                    if (edge.islayOn(rect.backEdge, rect.h + 65, 0.5)) {
                        let r_center = wall.rect.rect_center;
                        wall.rect.nor = edge.nor;
                        wall.rect.rect_center = r_center;
                    }
                }
            }
        }

        this._out_border_polygons = outerPolygons;
    }

    public getOutBorderPolygons(): ZPolygon[] {
        return this._out_border_polygons;
    }
}