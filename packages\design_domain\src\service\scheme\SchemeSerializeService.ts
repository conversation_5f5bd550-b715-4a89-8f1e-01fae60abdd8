import { I_SwjXmlScheme } from "@layoutai/basic_data";

import { ErrorConst } from "../../api/ErrorConst";
import { LayoutSchemeData } from "../../api/ILayoutScheme";
import { ReqResMsg } from "../../api/ReqResMsg";
import { SchemeConst } from "../../api/SchemeConst";
import { base64ToUtf8 } from "../../utils/xml_utils";
import { ISchemeSerialize } from "./ISchemeSerialize";
import { SchemeSerialize05 } from "./SchemeSerialize05";
import { ServiceManager } from "../ServiceManager";


/**
* @description 方案序列化服务
* <AUTHOR>
* @date 2025-06-25
* @lastEditTime 2025-06-25 10:21:16
* @lastEditors xuld
*/
export class SchemeSerializeService {

    private _schemeVersionHandlerMap: Map<string, ISchemeSerialize> = new Map();

    constructor() {
        this._schemeVersionHandlerMap.set(SchemeConst.version_0_5, new SchemeSerialize05());
    }

    /**
     * @description 序列化方案数据
     * @param schemeData 方案数据
     * @returns 序列化后的方案数据
     */
    public async serializeSchemeData(): Promise<ReqResMsg> {
        let schemeData: LayoutSchemeData = {
            area: 0,
            contentUrl: "",
            coverImage: "",
            createDate: "",
            createUser: "",
            id: "",
            isDelete: 0,
            layoutSchemeName: "",
            svjSchemeId: "",
            tenantId: "",
            updateDate: "",
            updateUser: "",
            dreamerScheme: "",
            projectId: ""
        };
        let res = new ReqResMsg();
        res.data = schemeData;
        return res;
    }


    private async getSchemeContentByUrl(schemeDataUrl: string): Promise<I_SwjXmlScheme | null> {

        const base64str = await fetch(schemeDataUrl).then((res) => res.text()).catch(e => null);
        if (!base64str) {
            return null;
        }

        const layoutSchemeJsonStr = base64ToUtf8(base64str);
        if (!layoutSchemeJsonStr) {
            return null;
        }

        const formatJsonStr = layoutSchemeJsonStr.replace(/'/g, '"');
        const schemeContentJson = JSON.parse(formatJsonStr);
        return schemeContentJson;
    }

    /**
     * @description 反序列化方案数据
     * @param schemeData 方案数据
     * @returns 反序列化后的方案数据
     */
    public async deserializeSchemeData(schemeData: LayoutSchemeData): Promise<ReqResMsg> {
        let res = new ReqResMsg();
        res.data = schemeData;

        let schemeDataUrl = schemeData.contentUrl;
        if (!schemeDataUrl) {
            res.success = false;
            res.errorCode = ErrorConst.ERROR_SCHEME_DATA_URL_IS_NULL.code;
            res.errorMsg = ErrorConst.ERROR_SCHEME_DATA_URL_IS_NULL.msg;
            return res;
        }

        let schemeContentJson = await this.getSchemeContentByUrl(schemeDataUrl);
        if (!schemeContentJson) {
            res.success = false;
            res.errorCode = ErrorConst.ERROR_SCHEME_CONTENT_IS_NULL.code;
            res.errorMsg = ErrorConst.ERROR_SCHEME_CONTENT_IS_NULL.msg;
            return res;
        }

        let version = schemeContentJson.LayoutAI_Version || "";
        let handler = this._schemeVersionHandlerMap.get(version);
        if (!handler) {
            res.success = false;
            res.errorCode = ErrorConst.ERROR_SCHEME_CONTENT_IS_NULL.code;
            res.errorMsg = ErrorConst.ERROR_SCHEME_CONTENT_IS_NULL.msg + ":" + version;
            return res;
        }

        await ServiceManager.instance.storeService.clearEntity();
        await handler.deserialize(schemeContentJson);

        return res;
    }
}