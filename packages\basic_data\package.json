{"name": "@layoutai/basic_data", "version": "1.0.0", "type": "module", "description": "", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"build": "tsc", "buildIndex": "cti entrypoint ./src -b -o index.ts", "update": "pnpm run buildIndex && pnpm run build"}, "devDependencies": {"create-ts-index": "^1.14.0", "rollup": "^4.41.1", "tsx": "^4.19.2", "typescript": "^5.6.3", "vite": "^5.2.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-dts": "^4.5.4", "vite-plugin-externals": "^0.6.2", "vite-plugin-html": "^3.2.2", "vite-tsconfig-paths": "^5.1.4", "@types/three": "^0.171.0"}, "dependencies": {"z_polygon": "workspace:^"}, "keywords": [], "author": "sunvega", "license": "ISC"}